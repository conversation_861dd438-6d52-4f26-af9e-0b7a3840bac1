# -*- coding: utf-8 -*-
"""
نماذج الويب لنظام إدارة صالة رياضية
Web Forms for Gym Management System
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, TextAreaField, SelectField, IntegerField, FloatField, DateField, BooleanField, PasswordField, SubmitField
from wtforms.validators import DataRequired, Email, Length, NumberRange, Optional, ValidationError
from wtforms.widgets import TextArea
from datetime import date, datetime
from models import User, Member

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(message='اسم المستخدم مطلوب')])
    password = PasswordField('كلمة المرور', validators=[DataRequired(message='كلمة المرور مطلوبة')])
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class MemberForm(FlaskForm):
    """نموذج إضافة/تعديل عضو"""
    full_name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم الكامل مطلوب'),
        Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')
    ])
    
    email = StringField('البريد الإلكتروني', validators=[
        Optional(),
        Email(message='البريد الإلكتروني غير صحيح')
    ])
    
    phone = StringField('رقم الهاتف', validators=[
        DataRequired(message='رقم الهاتف مطلوب'),
        Length(min=10, max=20, message='رقم الهاتف غير صحيح')
    ])
    
    national_id = StringField('رقم الهوية', validators=[
        Optional(),
        Length(min=10, max=20, message='رقم الهوية غير صحيح')
    ])
    
    date_of_birth = DateField('تاريخ الميلاد', validators=[Optional()])
    
    gender = SelectField('الجنس', choices=[
        ('male', 'ذكر'),
        ('female', 'أنثى')
    ], validators=[DataRequired(message='الجنس مطلوب')])
    
    address = TextAreaField('العنوان', validators=[Optional()])
    
    emergency_contact = StringField('جهة الاتصال في حالات الطوارئ', validators=[
        Optional(),
        Length(max=100)
    ])
    
    emergency_phone = StringField('هاتف الطوارئ', validators=[
        Optional(),
        Length(max=20)
    ])
    
    medical_conditions = TextAreaField('الحالات الطبية', validators=[Optional()])
    
    photo = FileField('الصورة الشخصية', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png'], 'يُسمح فقط بملفات الصور (JPG, PNG)')
    ])
    
    submit = SubmitField('حفظ')
    
    def validate_email(self, email):
        """التحقق من عدم تكرار البريد الإلكتروني"""
        if email.data:
            member = Member.query.filter_by(email=email.data).first()
            if member:
                raise ValidationError('هذا البريد الإلكتروني مسجل مسبقاً')
    
    def validate_national_id(self, national_id):
        """التحقق من عدم تكرار رقم الهوية"""
        if national_id.data:
            member = Member.query.filter_by(national_id=national_id.data).first()
            if member:
                raise ValidationError('رقم الهوية مسجل مسبقاً')
    
    def validate_date_of_birth(self, date_of_birth):
        """التحقق من صحة تاريخ الميلاد"""
        if date_of_birth.data:
            if date_of_birth.data > date.today():
                raise ValidationError('تاريخ الميلاد لا يمكن أن يكون في المستقبل')
            
            age = (date.today() - date_of_birth.data).days // 365
            if age < 16:
                raise ValidationError('العمر يجب أن يكون 16 سنة على الأقل')

class MembershipPlanForm(FlaskForm):
    """نموذج إضافة/تعديل باقة عضوية"""
    name = StringField('اسم الباقة', validators=[
        DataRequired(message='اسم الباقة مطلوب'),
        Length(min=2, max=100, message='اسم الباقة يجب أن يكون بين 2 و 100 حرف')
    ])
    
    description = TextAreaField('الوصف', validators=[Optional()])
    
    duration_months = IntegerField('المدة (بالأشهر)', validators=[
        DataRequired(message='المدة مطلوبة'),
        NumberRange(min=1, max=24, message='المدة يجب أن تكون بين 1 و 24 شهر')
    ])
    
    price = FloatField('السعر', validators=[
        DataRequired(message='السعر مطلوب'),
        NumberRange(min=0, message='السعر يجب أن يكون أكبر من صفر')
    ])
    
    features = TextAreaField('الميزات المتاحة', validators=[Optional()])
    
    max_classes_per_month = IntegerField('عدد الحصص المسموحة شهرياً', validators=[
        Optional(),
        NumberRange(min=0, max=100, message='عدد الحصص يجب أن يكون بين 0 و 100')
    ])
    
    submit = SubmitField('حفظ')

class PaymentForm(FlaskForm):
    """نموذج إضافة دفعة"""
    amount = FloatField('المبلغ', validators=[
        DataRequired(message='المبلغ مطلوب'),
        NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
    ])
    
    payment_method = SelectField('طريقة الدفع', choices=[
        ('cash', 'نقدي'),
        ('card', 'بطاقة ائتمان'),
        ('transfer', 'تحويل بنكي')
    ], validators=[DataRequired(message='طريقة الدفع مطلوبة')])
    
    description = StringField('الوصف', validators=[
        Optional(),
        Length(max=255, message='الوصف طويل جداً')
    ])
    
    submit = SubmitField('تسجيل الدفعة')

class MembershipForm(FlaskForm):
    """نموذج إضافة عضوية جديدة"""
    member_id = SelectField('العضو', coerce=int, validators=[
        DataRequired(message='اختيار العضو مطلوب')
    ])
    
    plan_id = SelectField('باقة العضوية', coerce=int, validators=[
        DataRequired(message='اختيار الباقة مطلوب')
    ])
    
    start_date = DateField('تاريخ البداية', validators=[
        DataRequired(message='تاريخ البداية مطلوب')
    ], default=date.today)
    
    discount = FloatField('الخصم', validators=[
        Optional(),
        NumberRange(min=0, max=100, message='الخصم يجب أن يكون بين 0 و 100')
    ], default=0)
    
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    
    submit = SubmitField('إنشاء العضوية')
    
    def validate_start_date(self, start_date):
        """التحقق من صحة تاريخ البداية"""
        if start_date.data < date.today():
            raise ValidationError('تاريخ البداية لا يمكن أن يكون في الماضي')

class TrainerForm(FlaskForm):
    """نموذج إضافة/تعديل مدرب"""
    user_id = SelectField('المستخدم', coerce=int, validators=[
        DataRequired(message='اختيار المستخدم مطلوب')
    ])
    
    specializations = TextAreaField('التخصصات', validators=[
        DataRequired(message='التخصصات مطلوبة')
    ])
    
    experience_years = IntegerField('سنوات الخبرة', validators=[
        Optional(),
        NumberRange(min=0, max=50, message='سنوات الخبرة يجب أن تكون بين 0 و 50')
    ])
    
    certifications = TextAreaField('الشهادات', validators=[Optional()])
    
    hourly_rate = FloatField('الأجر بالساعة', validators=[
        Optional(),
        NumberRange(min=0, message='الأجر يجب أن يكون أكبر من صفر')
    ])
    
    commission_rate = FloatField('نسبة العمولة (%)', validators=[
        Optional(),
        NumberRange(min=0, max=100, message='نسبة العمولة يجب أن تكون بين 0 و 100')
    ])
    
    bio = TextAreaField('نبذة شخصية', validators=[Optional()])
    
    photo = FileField('الصورة الشخصية', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png'], 'يُسمح فقط بملفات الصور (JPG, PNG)')
    ])
    
    submit = SubmitField('حفظ')

class GymClassForm(FlaskForm):
    """نموذج إضافة/تعديل فصل رياضي"""
    name = StringField('اسم الفصل', validators=[
        DataRequired(message='اسم الفصل مطلوب'),
        Length(min=2, max=100, message='اسم الفصل يجب أن يكون بين 2 و 100 حرف')
    ])
    
    description = TextAreaField('الوصف', validators=[Optional()])
    
    trainer_id = SelectField('المدرب', coerce=int, validators=[
        DataRequired(message='اختيار المدرب مطلوب')
    ])
    
    duration_minutes = IntegerField('المدة (بالدقائق)', validators=[
        DataRequired(message='المدة مطلوبة'),
        NumberRange(min=15, max=180, message='المدة يجب أن تكون بين 15 و 180 دقيقة')
    ])
    
    max_participants = IntegerField('العدد الأقصى للمشاركين', validators=[
        DataRequired(message='العدد الأقصى مطلوب'),
        NumberRange(min=1, max=50, message='العدد يجب أن يكون بين 1 و 50')
    ])
    
    price = FloatField('السعر', validators=[
        Optional(),
        NumberRange(min=0, message='السعر يجب أن يكون أكبر من صفر')
    ])
    
    class_type = SelectField('نوع الفصل', choices=[
        ('group', 'جماعي'),
        ('personal', 'شخصي'),
        ('online', 'أونلاين')
    ], validators=[DataRequired(message='نوع الفصل مطلوب')])
    
    submit = SubmitField('حفظ')

class EquipmentForm(FlaskForm):
    """نموذج إضافة/تعديل معدة"""
    name = StringField('اسم المعدة', validators=[
        DataRequired(message='اسم المعدة مطلوب'),
        Length(min=2, max=100, message='اسم المعدة يجب أن يكون بين 2 و 100 حرف')
    ])
    
    category = SelectField('الفئة', choices=[
        ('cardio', 'كارديو'),
        ('strength', 'قوة'),
        ('functional', 'وظيفي'),
        ('other', 'أخرى')
    ], validators=[DataRequired(message='الفئة مطلوبة')])
    
    brand = StringField('الماركة', validators=[Optional(), Length(max=50)])
    
    model = StringField('الموديل', validators=[Optional(), Length(max=50)])
    
    serial_number = StringField('الرقم التسلسلي', validators=[
        Optional(),
        Length(max=100)
    ])
    
    purchase_date = DateField('تاريخ الشراء', validators=[Optional()])
    
    purchase_price = FloatField('سعر الشراء', validators=[
        Optional(),
        NumberRange(min=0, message='السعر يجب أن يكون أكبر من صفر')
    ])
    
    warranty_expiry = DateField('انتهاء الضمان', validators=[Optional()])
    
    location = StringField('الموقع', validators=[Optional(), Length(max=100)])
    
    submit = SubmitField('حفظ')
