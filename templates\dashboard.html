{% extends "base.html" %}

{% block title %}لوحة التحكم - جيم الأبطال{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="text-gradient">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </h1>
                <div class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    {{ moment().format('dddd، DD MMMM YYYY') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="stats-number">{{ stats.total_members }}</div>
                <div class="stats-label">
                    <i class="fas fa-users me-1"></i>
                    إجمالي الأعضاء
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="stats-number">{{ stats.active_memberships }}</div>
                <div class="stats-label">
                    <i class="fas fa-id-card me-1"></i>
                    العضويات النشطة
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="stats-number">{{ "%.0f"|format(stats.total_revenue_month) }} ر.س</div>
                <div class="stats-label">
                    <i class="fas fa-money-bill me-1"></i>
                    إيرادات الشهر
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-number">{{ stats.classes_today }}</div>
                <div class="stats-label">
                    <i class="fas fa-dumbbell me-1"></i>
                    حصص اليوم
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الأعضاء الجدد -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        الأعضاء الجدد هذا الأسبوع
                    </h5>
                </div>
                <div class="card-body">
                    {% if new_members %}
                        <div class="list-group list-group-flush">
                            {% for member in new_members %}
                            <div class="list-group-item d-flex align-items-center border-0 px-0">
                                <div class="me-3">
                                    {% if member.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + member.photo) }}" 
                                             alt="{{ member.full_name }}" class="profile-img">
                                    {% else %}
                                        <div class="profile-img bg-secondary d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ member.full_name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-id-badge me-1"></i>
                                        {{ member.member_id }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ member.registration_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge status-{{ 'active' if member.is_active else 'expired' }}">
                                        {{ member.membership_status }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('members_list') }}" class="btn btn-outline-primary">
                                عرض جميع الأعضاء
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-3x mb-3 opacity-50"></i>
                            <p>لا توجد تسجيلات جديدة هذا الأسبوع</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- العضويات المنتهية قريباً -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        عضويات تنتهي قريباً
                    </h5>
                </div>
                <div class="card-body">
                    {% if expiring_memberships %}
                        <div class="list-group list-group-flush">
                            {% for membership in expiring_memberships %}
                            <div class="list-group-item d-flex align-items-center border-0 px-0">
                                <div class="me-3">
                                    {% if membership.member.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + membership.member.photo) }}" 
                                             alt="{{ membership.member.full_name }}" class="profile-img">
                                    {% else %}
                                        <div class="profile-img bg-warning d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ membership.member.full_name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-id-card me-1"></i>
                                        {{ membership.plan.name }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-times me-1"></i>
                                        ينتهي في {{ membership.end_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge status-expiring">
                                        {{ membership.days_remaining }} يوم
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('members_list', status='expiring') }}" class="btn btn-outline-warning">
                                عرض جميع المنتهية
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 opacity-50"></i>
                            <p>جميع العضويات سارية المفعول</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('add_member') }}" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <span>تسجيل عضو جديد</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('payments_list') }}" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <span>تسجيل دفعة</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ url_for('members_list') }}" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-search fa-2x mb-2"></i>
                                <span>البحث عن عضو</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="#" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    const timeString = now.toLocaleDateString('ar-SA', options);
    document.querySelector('.text-muted').innerHTML = '<i class="fas fa-calendar me-1"></i>' + timeString;
}

// تحديث الوقت عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateTime();
    setInterval(updateTime, 60000); // تحديث كل دقيقة
});
</script>
{% endblock %}
