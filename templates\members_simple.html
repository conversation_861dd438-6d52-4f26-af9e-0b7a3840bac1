<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأعضاء - جيم الأبطال</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }
        
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .status-active {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .status-inactive {
            background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-dumbbell me-2"></i>
                جيم الأبطال
            </a>
            
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link active" href="{{ url_for('members') }}">
                    <i class="fas fa-users me-1"></i>
                    الأعضاء
                </a>
            </div>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        {{ current_user.full_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 style="color: #667eea; font-weight: 700;">
                        <i class="fas fa-users me-2"></i>
                        إدارة الأعضاء
                    </h1>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عضو جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="البحث عن عضو...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select">
                                    <option value="">جميع الأجناس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Members Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الأعضاء ({{ members|length }} عضو)
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if members %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم العضوية</th>
                                            <th>الاسم الكامل</th>
                                            <th>رقم الهاتف</th>
                                            <th>الجنس</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for member in members %}
                                        <tr>
                                            <td>
                                                <strong>{{ member.member_id }}</strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <strong>{{ member.full_name }}</strong>
                                                        {% if member.email %}
                                                            <br><small class="text-muted">{{ member.email }}</small>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="fas fa-phone me-1"></i>
                                                {{ member.phone }}
                                            </td>
                                            <td>
                                                {% if member.gender == 'male' %}
                                                    <i class="fas fa-mars text-primary me-1"></i>
                                                    ذكر
                                                {% else %}
                                                    <i class="fas fa-venus text-danger me-1"></i>
                                                    أنثى
                                                {% endif %}
                                            </td>
                                            <td>
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ member.registration_date.strftime('%d/%m/%Y') }}
                                            </td>
                                            <td>
                                                {% if member.is_active %}
                                                    <span class="badge status-active">
                                                        <i class="fas fa-check me-1"></i>
                                                        نشط
                                                    </span>
                                                {% else %}
                                                    <span class="badge status-inactive">
                                                        <i class="fas fa-times me-1"></i>
                                                        غير نشط
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد أعضاء مسجلين</h4>
                                <p class="text-muted">ابدأ بإضافة أول عضو في الصالة الرياضية</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة عضو جديد
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Summary -->
        {% if members %}
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{{ members|selectattr('is_active')|list|length }}</h3>
                        <p class="text-muted">أعضاء نشطين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">{{ members|selectattr('gender', 'equalto', 'male')|list|length }}</h3>
                        <p class="text-muted">أعضاء ذكور</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-danger">{{ members|selectattr('gender', 'equalto', 'female')|list|length }}</h3>
                        <p class="text-muted">أعضاء إناث</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer class="text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 جيم الأبطال - نظام إدارة الصالة الرياضية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
