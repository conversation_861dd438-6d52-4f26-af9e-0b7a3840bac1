<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - جيم الأبطال</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-header h3 {
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .sidebar-menu .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .sidebar-menu .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            padding-right: 2rem;
        }
        
        .sidebar-menu .menu-item.active {
            background: rgba(255,255,255,0.2);
            border-right: 4px solid white;
        }
        
        .sidebar-menu .menu-item i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            border-radius: 15px;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .stats-card.primary .icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stats-card.success .icon {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        
        .stats-card.warning .icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .stats-card.info .icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            color: #2c3e50;
        }
        
        .stats-card p {
            margin: 0;
            color: #7f8c8d;
            font-weight: 500;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .btn-action {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-dumbbell fa-2x mb-2"></i>
            <h3>جيم الأبطال</h3>
            <p class="mb-0">نظام الإدارة</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('index') }}" class="menu-item active">
                <i class="fas fa-home"></i>
                الرئيسية
            </a>
            <a href="{{ url_for('members') }}" class="menu-item">
                <i class="fas fa-users"></i>
                إدارة الأعضاء
            </a>
            <a href="{{ url_for('memberships') }}" class="menu-item">
                <i class="fas fa-id-card"></i>
                باقات العضوية
            </a>
            <a href="{{ url_for('payments') }}" class="menu-item">
                <i class="fas fa-credit-card"></i>
                المدفوعات
            </a>
            <a href="{{ url_for('trainers') }}" class="menu-item">
                <i class="fas fa-user-tie"></i>
                المدربين
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-calendar-alt"></i>
                جدولة الحصص
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-dumbbell"></i>
                إدارة المعدات
            </a>
            <a href="{{ url_for('reports') }}" class="menu-item">
                <i class="fas fa-chart-bar"></i>
                التقارير
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
            <a href="{{ url_for('logout') }}" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <div class="top-navbar">
            <div>
                <h4 class="mb-0" style="color: #667eea; font-weight: 700;">
                    <i class="fas fa-home me-2"></i>
                    لوحة التحكم الرئيسية
                </h4>
            </div>
            <div>
                <span class="text-muted">مرحباً، </span>
                <strong style="color: #667eea;">{{ current_user.full_name }}</strong>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card primary">
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>{{ stats.total_members }}</h3>
                    <p>إجمالي الأعضاء</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card success">
                    <div class="icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3>{{ stats.active_memberships }}</h3>
                    <p>العضويات النشطة</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card warning">
                    <div class="icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h3>{{ "%.0f"|format(stats.total_revenue_month) }} ر.س</h3>
                    <p>إيرادات الشهر</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card info">
                    <div class="icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3>{{ stats.classes_today }}</h3>
                    <p>حصص اليوم</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="quick-actions">
                    <h5 class="mb-4" style="color: #667eea; font-weight: 700;">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('add_member') }}" class="btn btn-action w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة عضو جديد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('add_payment') }}" class="btn btn-action w-100">
                                <i class="fas fa-credit-card me-2"></i>
                                تسجيل دفعة
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('add_membership_plan') }}" class="btn btn-action w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة باقة عضوية
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('add_trainer') }}" class="btn btn-action w-100">
                                <i class="fas fa-user-tie me-2"></i>
                                إضافة مدرب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تفعيل العنصر النشط في القائمة الجانبية
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-menu .menu-item');

            menuItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
