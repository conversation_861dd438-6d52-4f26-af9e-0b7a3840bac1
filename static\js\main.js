/**
 * ملف JavaScript الرئيسي لنظام إدارة جيم الأبطال
 * Main JavaScript file for Champions Gym Management System
 */

$(document).ready(function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة المكونات
    initializeComponents();
    
    // تهيئة الأحداث
    initializeEvents();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء رسائل التنبيه تلقائياً
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

/**
 * تهيئة المكونات
 */
function initializeComponents() {
    // تهيئة البحث المباشر
    initializeLiveSearch();
    
    // تهيئة رفع الملفات
    initializeFileUpload();
    
    // تهيئة التحقق من النماذج
    initializeFormValidation();
    
    // تهيئة الجداول
    initializeTables();
}

/**
 * تهيئة الأحداث
 */
function initializeEvents() {
    // حدث النقر على أزرار الحذف
    $(document).on('click', '.btn-delete', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const itemName = $(this).data('item-name') || 'هذا العنصر';
        
        showConfirmDialog(
            'تأكيد الحذف',
            `هل أنت متأكد من حذف ${itemName}؟`,
            'حذف',
            'إلغاء',
            function() {
                window.location.href = url;
            }
        );
    });
    
    // حدث تغيير حالة العضو
    $(document).on('change', '.member-status-toggle', function() {
        const memberId = $(this).data('member-id');
        const isActive = $(this).is(':checked');
        
        updateMemberStatus(memberId, isActive);
    });
    
    // حدث البحث السريع
    $(document).on('input', '.quick-search', function() {
        const query = $(this).val();
        const targetTable = $(this).data('target');
        
        if (query.length >= 2) {
            performQuickSearch(query, targetTable);
        } else {
            clearSearchResults(targetTable);
        }
    });
}

/**
 * تهيئة البحث المباشر
 */
function initializeLiveSearch() {
    $('.member-search').on('input', function() {
        const query = $(this).val();
        
        if (query.length >= 2) {
            $.ajax({
                url: '/api/members/search',
                data: { q: query },
                success: function(data) {
                    displaySearchResults(data, '.search-results');
                }
            });
        } else {
            $('.search-results').empty();
        }
    });
}

/**
 * تهيئة رفع الملفات
 */
function initializeFileUpload() {
    $('.file-upload').on('change', function() {
        const file = this.files[0];
        const preview = $(this).siblings('.file-preview');
        
        if (file) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.html(`<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">`);
                };
                reader.readAsDataURL(file);
            } else {
                preview.html(`<p class="text-muted">تم اختيار الملف: ${file.name}</p>`);
            }
        }
    });
}

/**
 * تهيئة التحقق من النماذج
 */
function initializeFormValidation() {
    // التحقق من رقم الهاتف
    $('.phone-input').on('input', function() {
        const phone = $(this).val();
        const isValid = /^[0-9+\-\s()]+$/.test(phone);
        
        if (!isValid && phone.length > 0) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('رقم الهاتف غير صحيح');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // التحقق من البريد الإلكتروني
    $('.email-input').on('input', function() {
        const email = $(this).val();
        const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        
        if (!isValid && email.length > 0) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('البريد الإلكتروني غير صحيح');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة فئات CSS للجداول
    $('.table').addClass('table-hover');
    
    // تفعيل الترتيب للجداول
    $('.sortable-table th').on('click', function() {
        const table = $(this).closest('table');
        const column = $(this).index();
        const order = $(this).hasClass('asc') ? 'desc' : 'asc';
        
        sortTable(table, column, order);
        
        // تحديث مؤشرات الترتيب
        table.find('th').removeClass('asc desc');
        $(this).addClass(order);
    });
}

/**
 * عرض نتائج البحث
 */
function displaySearchResults(data, container) {
    const resultsHtml = data.map(item => `
        <div class="search-result-item p-2 border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${item.full_name}</strong>
                    <br>
                    <small class="text-muted">${item.member_id} - ${item.phone}</small>
                </div>
                <span class="badge bg-${getStatusColor(item.status)}">${item.status}</span>
            </div>
        </div>
    `).join('');
    
    $(container).html(resultsHtml);
}

/**
 * تحديث حالة العضو
 */
function updateMemberStatus(memberId, isActive) {
    $.ajax({
        url: `/api/members/${memberId}/status`,
        method: 'POST',
        data: { is_active: isActive },
        success: function(response) {
            showToast('تم تحديث حالة العضو بنجاح', 'success');
        },
        error: function() {
            showToast('حدث خطأ أثناء تحديث حالة العضو', 'error');
        }
    });
}

/**
 * عرض رسالة تأكيد
 */
function showConfirmDialog(title, message, confirmText, cancelText, onConfirm) {
    const modal = $(`
        <div class="modal fade" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                        <button type="button" class="btn btn-danger confirm-btn">${confirmText}</button>
                    </div>
                </div>
            </div>
        </div>
    `);
    
    modal.find('.confirm-btn').on('click', function() {
        onConfirm();
        modal.modal('hide');
    });
    
    modal.on('hidden.bs.modal', function() {
        modal.remove();
    });
    
    $('body').append(modal);
    modal.modal('show');
}

/**
 * عرض رسالة منبثقة
 */
function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `);
    
    // إنشاء حاوي للرسائل إذا لم يكن موجوداً
    if (!$('.toast-container').length) {
        $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }
    
    $('.toast-container').append(toast);
    
    const bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
    
    // إزالة الرسالة بعد إخفائها
    toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

/**
 * الحصول على لون الحالة
 */
function getStatusColor(status) {
    switch (status) {
        case 'نشطة':
            return 'success';
        case 'منتهية':
            return 'danger';
        case 'تنتهي قريباً':
            return 'warning';
        default:
            return 'secondary';
    }
}

/**
 * ترتيب الجدول
 */
function sortTable(table, column, order) {
    const rows = table.find('tbody tr').toArray();
    
    rows.sort(function(a, b) {
        const aVal = $(a).find('td').eq(column).text().trim();
        const bVal = $(b).find('td').eq(column).text().trim();
        
        if (order === 'asc') {
            return aVal.localeCompare(bVal, 'ar');
        } else {
            return bVal.localeCompare(aVal, 'ar');
        }
    });
    
    table.find('tbody').empty().append(rows);
}

/**
 * تنسيق الأرقام العربية
 */
function formatArabicNumber(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, function(w) {
        return arabicNumbers[+w];
    });
}

/**
 * تنسيق التاريخ العربي
 */
function formatArabicDate(date) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    return new Date(date).toLocaleDateString('ar-SA', options);
}

/**
 * تحميل البيانات بشكل غير متزامن
 */
function loadData(url, container, loadingText = 'جاري التحميل...') {
    $(container).html(`<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> ${loadingText}</div>`);
    
    $.ajax({
        url: url,
        success: function(data) {
            $(container).html(data);
        },
        error: function() {
            $(container).html('<div class="text-center text-danger p-4">حدث خطأ أثناء تحميل البيانات</div>');
        }
    });
}

/**
 * تصدير البيانات
 */
function exportData(format, url) {
    const link = document.createElement('a');
    link.href = `${url}?format=${format}`;
    link.download = `gym_data_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تصدير الوظائف للاستخدام العام
window.GymApp = {
    showToast,
    showConfirmDialog,
    loadData,
    exportData,
    formatArabicNumber,
    formatArabicDate
};
