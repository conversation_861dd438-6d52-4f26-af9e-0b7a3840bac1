#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل بسيط لنظام إدارة جيم الأبطال
Simple runner for Champions Gym Management System
"""

from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import os

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///gym_champions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة الإضافات
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# النماذج البسيطة
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='employee')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Member(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    member_id = db.Column(db.String(20), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    birth_date = db.Column(db.Date)
    address = db.Column(db.Text)
    emergency_contact = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    registration_date = db.Column(db.DateTime, default=datetime.now)

class MembershipPlan(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    duration_days = db.Column(db.Integer, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Membership(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    member_id = db.Column(db.Integer, db.ForeignKey('member.id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('membership_plan.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

    member = db.relationship('Member', backref='memberships')
    plan = db.relationship('MembershipPlan', backref='memberships')

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    member_id = db.Column(db.Integer, db.ForeignKey('member.id'), nullable=False)
    membership_id = db.Column(db.Integer, db.ForeignKey('membership.id'))
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)
    payment_date = db.Column(db.DateTime, default=datetime.now)
    notes = db.Column(db.Text)

    member = db.relationship('Member', backref='payments')
    membership = db.relationship('Membership', backref='payments')

class Trainer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20), nullable=False)
    specialization = db.Column(db.String(100))
    salary = db.Column(db.Float)
    hire_date = db.Column(db.Date, nullable=False)
    is_active = db.Column(db.Boolean, default=True)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات
@app.route('/')
def index():
    if not current_user.is_authenticated:
        return redirect(url_for('login'))

    stats = {
        'total_members': Member.query.filter_by(is_active=True).count(),
        'active_memberships': Membership.query.filter_by(is_active=True).count(),
        'total_revenue_month': db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.payment_date >= datetime.now().replace(day=1)
        ).scalar() or 0,
        'classes_today': 0
    }

    return render_template('dashboard.html', stats=stats)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_simple.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/members')
@login_required
def members():
    members = Member.query.all()
    return render_template('members.html', members=members)

@app.route('/members/add', methods=['GET', 'POST'])
@login_required
def add_member():
    if request.method == 'POST':
        # إنشاء رقم عضوية جديد
        last_member = Member.query.order_by(Member.id.desc()).first()
        member_id = f"M{(last_member.id + 1) if last_member else 1:04d}"

        member = Member(
            member_id=member_id,
            full_name=request.form['full_name'],
            email=request.form.get('email'),
            phone=request.form['phone'],
            gender=request.form['gender'],
            birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form.get('birth_date') else None,
            address=request.form.get('address'),
            emergency_contact=request.form.get('emergency_contact')
        )

        db.session.add(member)
        db.session.commit()
        flash('تم إضافة العضو بنجاح!', 'success')
        return redirect(url_for('members'))

    return render_template('add_member.html')

@app.route('/memberships')
@login_required
def memberships():
    plans = MembershipPlan.query.filter_by(is_active=True).all()
    return render_template('memberships.html', plans=plans)

@app.route('/memberships/add', methods=['GET', 'POST'])
@login_required
def add_membership_plan():
    if request.method == 'POST':
        plan = MembershipPlan(
            name=request.form['name'],
            description=request.form.get('description'),
            price=float(request.form['price']),
            duration_days=int(request.form['duration_days'])
        )

        db.session.add(plan)
        db.session.commit()
        flash('تم إضافة باقة العضوية بنجاح!', 'success')
        return redirect(url_for('memberships'))

    return render_template('add_membership_plan.html')

@app.route('/payments')
@login_required
def payments():
    payments = Payment.query.order_by(Payment.payment_date.desc()).all()
    return render_template('payments.html', payments=payments)

@app.route('/payments/add', methods=['GET', 'POST'])
@login_required
def add_payment():
    if request.method == 'POST':
        payment = Payment(
            member_id=int(request.form['member_id']),
            amount=float(request.form['amount']),
            payment_method=request.form['payment_method'],
            notes=request.form.get('notes')
        )

        db.session.add(payment)
        db.session.commit()
        flash('تم تسجيل الدفعة بنجاح!', 'success')
        return redirect(url_for('payments'))

    members = Member.query.filter_by(is_active=True).all()
    return render_template('add_payment.html', members=members)

@app.route('/trainers')
@login_required
def trainers():
    trainers = Trainer.query.filter_by(is_active=True).all()
    return render_template('trainers.html', trainers=trainers)

@app.route('/trainers/add', methods=['GET', 'POST'])
@login_required
def add_trainer():
    if request.method == 'POST':
        trainer = Trainer(
            full_name=request.form['full_name'],
            email=request.form.get('email'),
            phone=request.form['phone'],
            specialization=request.form.get('specialization'),
            salary=float(request.form['salary']) if request.form.get('salary') else None,
            hire_date=datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date()
        )

        db.session.add(trainer)
        db.session.commit()
        flash('تم إضافة المدرب بنجاح!', 'success')
        return redirect(url_for('trainers'))

    return render_template('add_trainer.html')

@app.route('/reports')
@login_required
def reports():
    # إحصائيات شاملة
    stats = {
        'total_members': Member.query.count(),
        'active_members': Member.query.filter_by(is_active=True).count(),
        'total_revenue': db.session.query(db.func.sum(Payment.amount)).scalar() or 0,
        'monthly_revenue': db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.payment_date >= datetime.now().replace(day=1)
        ).scalar() or 0,
        'total_trainers': Trainer.query.filter_by(is_active=True).count(),
        'membership_plans': MembershipPlan.query.filter_by(is_active=True).count()
    }

    return render_template('reports.html', stats=stats)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم مدير افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            
            # إضافة بعض الأعضاء التجريبيين
            members_data = [
                {'member_id': 'GYM00001', 'full_name': 'أحمد محمد علي', 'phone': '0501234567', 'gender': 'male'},
                {'member_id': 'GYM00002', 'full_name': 'فاطمة أحمد سالم', 'phone': '0507654321', 'gender': 'female'},
                {'member_id': 'GYM00003', 'full_name': 'محمد عبدالله حسن', 'phone': '0509876543', 'gender': 'male'},
            ]

            for member_data in members_data:
                member = Member(**member_data)
                db.session.add(member)

            # إنشاء باقات العضوية التجريبية
            sample_plans = [
                MembershipPlan(name="عضوية شهرية", description="عضوية لمدة شهر واحد", price=200.0, duration_days=30),
                MembershipPlan(name="عضوية ربع سنوية", description="عضوية لمدة 3 أشهر", price=500.0, duration_days=90),
                MembershipPlan(name="عضوية نصف سنوية", description="عضوية لمدة 6 أشهر", price=900.0, duration_days=180),
                MembershipPlan(name="عضوية سنوية", description="عضوية لمدة سنة كاملة", price=1500.0, duration_days=365)
            ]

            # إنشاء مدربين تجريبيين
            sample_trainers = [
                Trainer(full_name="كابتن أحمد", email="<EMAIL>", phone="01111111111",
                       specialization="تدريب الأثقال", salary=3000.0, hire_date=date(2024, 1, 1)),
                Trainer(full_name="كابتن سارة", email="<EMAIL>", phone="01111111112",
                       specialization="اللياقة البدنية", salary=2800.0, hire_date=date(2024, 2, 1))
            ]

            for plan in sample_plans:
                db.session.add(plan)

            for trainer in sample_trainers:
                db.session.add(trainer)
            
            db.session.commit()
            print("تم إنشاء حساب المدير الافتراضي:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
