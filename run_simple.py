#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل بسيط لنظام إدارة جيم الأبطال
Simple runner for Champions Gym Management System
"""

from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import os

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///gym_champions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة الإضافات
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# النماذج البسيطة
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='employee')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Member(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    member_id = db.Column(db.String(20), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    registration_date = db.Column(db.DateTime, default=datetime.now)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات
@app.route('/')
def index():
    if not current_user.is_authenticated:
        return redirect(url_for('login'))
    
    stats = {
        'total_members': Member.query.filter_by(is_active=True).count(),
        'active_memberships': 0,
        'total_revenue_month': 0,
        'classes_today': 0
    }
    
    return render_template('dashboard_simple.html', stats=stats)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_simple.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/members')
@login_required
def members():
    members = Member.query.all()
    return render_template('members_simple.html', members=members)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم مدير افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            
            # إضافة بعض الأعضاء التجريبيين
            members_data = [
                {'member_id': 'GYM00001', 'full_name': 'أحمد محمد علي', 'phone': '0501234567', 'gender': 'male'},
                {'member_id': 'GYM00002', 'full_name': 'فاطمة أحمد سالم', 'phone': '0507654321', 'gender': 'female'},
                {'member_id': 'GYM00003', 'full_name': 'محمد عبدالله حسن', 'phone': '0509876543', 'gender': 'male'},
            ]
            
            for member_data in members_data:
                member = Member(**member_data)
                db.session.add(member)
            
            db.session.commit()
            print("تم إنشاء حساب المدير الافتراضي:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
