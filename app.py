#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة صالة رياضية - جيم الأبطال
Champions Gym Management System

تطبيق Flask لإدارة صالة رياضية مع دعم كامل للغة العربية والـ RTL
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from datetime import datetime
import os
import secrets

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///gym_champions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# تهيئة الإضافات
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# تهيئة قاعدة البيانات في النماذج
import models
models.db = db

# استيراد النماذج
from models import User

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم"""
    return User.query.get(int(user_id))

# استيراد المسارات
import routes

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # إنشاء مستخدم مدير افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء حساب المدير الافتراضي:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
