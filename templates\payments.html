<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المدفوعات - جيم الأبطال</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-header h3 {
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .sidebar-menu .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .sidebar-menu .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            padding-right: 2rem;
        }
        
        .sidebar-menu .menu-item.active {
            background: rgba(255,255,255,0.2);
            border-right: 4px solid white;
        }
        
        .sidebar-menu .menu-item i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            border-radius: 15px;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }
        
        .amount-badge {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .payment-method-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .payment-method-cash {
            background-color: #28a745;
            color: white;
        }
        
        .payment-method-card {
            background-color: #007bff;
            color: white;
        }
        
        .payment-method-transfer {
            background-color: #6f42c1;
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-dumbbell fa-2x mb-2"></i>
            <h3>جيم الأبطال</h3>
            <p class="mb-0">نظام الإدارة</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('index') }}" class="menu-item">
                <i class="fas fa-home"></i>
                الرئيسية
            </a>
            <a href="{{ url_for('members') }}" class="menu-item">
                <i class="fas fa-users"></i>
                إدارة الأعضاء
            </a>
            <a href="{{ url_for('memberships') }}" class="menu-item">
                <i class="fas fa-id-card"></i>
                باقات العضوية
            </a>
            <a href="{{ url_for('payments') }}" class="menu-item active">
                <i class="fas fa-credit-card"></i>
                المدفوعات
            </a>
            <a href="{{ url_for('trainers') }}" class="menu-item">
                <i class="fas fa-user-tie"></i>
                المدربين
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-calendar-alt"></i>
                جدولة الحصص
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-dumbbell"></i>
                إدارة المعدات
            </a>
            <a href="{{ url_for('reports') }}" class="menu-item">
                <i class="fas fa-chart-bar"></i>
                التقارير
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
            <a href="{{ url_for('logout') }}" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <div class="top-navbar">
            <div>
                <h4 class="mb-0" style="color: #667eea; font-weight: 700;">
                    <i class="fas fa-credit-card me-2"></i>
                    إدارة المدفوعات
                </h4>
            </div>
            <div>
                <span class="text-muted">مرحباً، </span>
                <strong style="color: #667eea;">{{ current_user.full_name }}</strong>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Content -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0" style="color: #667eea; font-weight: 700;">
                    <i class="fas fa-list me-2"></i>
                    سجل المدفوعات
                </h5>
                <a href="{{ url_for('add_payment') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    تسجيل دفعة جديدة
                </a>
            </div>

            {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العضو</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>تاريخ الدفع</th>
                                <th>الملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>
                                    <strong>{{ payment.member.full_name }}</strong><br>
                                    <small class="text-muted">{{ payment.member.member_id }}</small>
                                </td>
                                <td>
                                    <span class="amount-badge">{{ payment.amount }} ر.س</span>
                                </td>
                                <td>
                                    {% if payment.payment_method == 'cash' %}
                                        <span class="payment-method-badge payment-method-cash">
                                            <i class="fas fa-money-bill-wave me-1"></i>نقدي
                                        </span>
                                    {% elif payment.payment_method == 'card' %}
                                        <span class="payment-method-badge payment-method-card">
                                            <i class="fas fa-credit-card me-1"></i>بطاقة
                                        </span>
                                    {% elif payment.payment_method == 'transfer' %}
                                        <span class="payment-method-badge payment-method-transfer">
                                            <i class="fas fa-exchange-alt me-1"></i>تحويل
                                        </span>
                                    {% else %}
                                        <span class="payment-method-badge" style="background-color: #6c757d; color: white;">
                                            {{ payment.payment_method }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ payment.notes or 'لا توجد ملاحظات' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مدفوعات مسجلة</h5>
                    <p class="text-muted">ابدأ بتسجيل أول دفعة</p>
                    <a href="{{ url_for('add_payment') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        تسجيل دفعة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
