#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق تجريبي بسيط لاختبار Flask
"""

try:
    from flask import Flask
    print("Flask imported successfully")
    
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>جيم الأبطال - اختبار</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                h1 { color: #667eea; }
            </style>
        </head>
        <body>
            <h1>🏋️ مرحباً بك في جيم الأبطال 🏋️</h1>
            <p>التطبيق يعمل بنجاح!</p>
            <p>Flask Application is running successfully!</p>
        </body>
        </html>
        '''
    
    if __name__ == '__main__':
        print("Starting Flask application...")
        app.run(debug=True, host='0.0.0.0', port=5000)
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install Flask: pip install flask")
except Exception as e:
    print(f"Error: {e}")
