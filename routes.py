# -*- coding: utf-8 -*-
"""
مسارات التطبيق لنظام إدارة صالة رياضية
Application Routes for Gym Management System
"""

from flask import render_template, request, redirect, url_for, flash, session, jsonify, send_file
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta, date
import os
import secrets
from PIL import Image

# استيراد النماذج والتطبيق
from flask import current_app as app
from flask_sqlalchemy import SQLAlchemy

# سيتم تعيين هذه المتغيرات من app.py
db = None

def init_routes(app_instance, db_instance):
    """تهيئة المسارات مع التطبيق وقاعدة البيانات"""
    global app, db
    app = app_instance
    db = db_instance

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if not current_user.is_authenticated:
        return redirect(url_for('login'))
    
    # إحصائيات سريعة للوحة التحكم
    stats = {
        'total_members': Member.query.filter_by(is_active=True).count(),
        'active_memberships': Membership.query.filter_by(is_active=True).count(),
        'total_revenue_month': db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.payment_date >= datetime.now().replace(day=1)
        ).scalar() or 0,
        'classes_today': ClassBooking.query.filter_by(
            booking_date=date.today(),
            status='confirmed'
        ).count()
    }
    
    # الأعضاء الجدد هذا الأسبوع
    week_ago = datetime.now() - timedelta(days=7)
    new_members = Member.query.filter(
        Member.registration_date >= week_ago
    ).order_by(Member.registration_date.desc()).limit(5).all()
    
    # العضويات المنتهية قريباً
    next_week = date.today() + timedelta(days=7)
    expiring_memberships = Membership.query.filter(
        Membership.end_date <= next_week,
        Membership.is_active == True
    ).order_by(Membership.end_date).limit(5).all()
    
    return render_template('dashboard.html', 
                         stats=stats, 
                         new_members=new_members,
                         expiring_memberships=expiring_memberships)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.now()
            db.session.commit()
            
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('index')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/members')
@login_required
def members_list():
    """قائمة الأعضاء"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    status = request.args.get('status', 'all', type=str)
    
    query = Member.query
    
    # البحث
    if search:
        query = query.filter(
            db.or_(
                Member.full_name.contains(search),
                Member.member_id.contains(search),
                Member.phone.contains(search),
                Member.email.contains(search)
            )
        )
    
    # فلترة حسب الحالة
    if status == 'active':
        query = query.filter_by(is_active=True)
    elif status == 'inactive':
        query = query.filter_by(is_active=False)
    
    members = query.order_by(Member.registration_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('members/list.html', members=members, search=search, status=status)

@app.route('/members/add', methods=['GET', 'POST'])
@login_required
def add_member():
    """إضافة عضو جديد"""
    form = MemberForm()
    
    if form.validate_on_submit():
        # إنشاء رقم عضوية فريد
        last_member = Member.query.order_by(Member.id.desc()).first()
        member_id = f"GYM{(last_member.id + 1) if last_member else 1:05d}"
        
        member = Member(
            member_id=member_id,
            full_name=form.full_name.data,
            email=form.email.data,
            phone=form.phone.data,
            national_id=form.national_id.data,
            date_of_birth=form.date_of_birth.data,
            gender=form.gender.data,
            address=form.address.data,
            emergency_contact=form.emergency_contact.data,
            emergency_phone=form.emergency_phone.data,
            medical_conditions=form.medical_conditions.data,
            created_by=current_user.id
        )
        
        # رفع الصورة إذا تم اختيارها
        if form.photo.data:
            photo_file = save_picture(form.photo.data, 'members')
            member.photo = photo_file
        
        db.session.add(member)
        db.session.commit()
        
        flash(f'تم تسجيل العضو {member.full_name} بنجاح برقم العضوية {member.member_id}', 'success')
        return redirect(url_for('member_detail', id=member.id))
    
    return render_template('members/add.html', form=form)

def save_picture(form_picture, folder):
    """حفظ الصورة"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(app.root_path, 'static', 'uploads', folder, picture_fn)
    
    # إنشاء المجلد إذا لم يكن موجوداً
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)
    
    # تصغير الصورة
    output_size = (300, 300)
    img = Image.open(form_picture)
    img.thumbnail(output_size)
    img.save(picture_path)
    
    return f'{folder}/{picture_fn}'

@app.route('/members/<int:id>')
@login_required
def member_detail(id):
    """تفاصيل العضو"""
    member = Member.query.get_or_404(id)
    
    # العضوية الحالية
    current_membership = member.current_membership
    
    # تاريخ المدفوعات
    payments = Payment.query.filter_by(member_id=id).order_by(Payment.payment_date.desc()).limit(10).all()
    
    # الحصص المحجوزة
    bookings = ClassBooking.query.filter_by(member_id=id).order_by(ClassBooking.booking_date.desc()).limit(10).all()
    
    return render_template('members/detail.html', 
                         member=member, 
                         current_membership=current_membership,
                         payments=payments,
                         bookings=bookings)

@app.route('/membership-plans')
@login_required
def membership_plans():
    """باقات العضوية"""
    plans = MembershipPlan.query.filter_by(is_active=True).all()
    return render_template('membership/plans.html', plans=plans)

@app.route('/membership-plans/add', methods=['GET', 'POST'])
@login_required
def add_membership_plan():
    """إضافة باقة عضوية جديدة"""
    if current_user.role != 'admin':
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    
    form = MembershipPlanForm()
    
    if form.validate_on_submit():
        plan = MembershipPlan(
            name=form.name.data,
            description=form.description.data,
            duration_months=form.duration_months.data,
            price=form.price.data,
            features=form.features.data,
            max_classes_per_month=form.max_classes_per_month.data
        )
        
        db.session.add(plan)
        db.session.commit()
        
        flash(f'تم إضافة باقة العضوية {plan.name} بنجاح', 'success')
        return redirect(url_for('membership_plans'))
    
    return render_template('membership/add_plan.html', form=form)

@app.route('/payments')
@login_required
def payments_list():
    """قائمة المدفوعات"""
    page = request.args.get('page', 1, type=int)
    payments = Payment.query.order_by(Payment.payment_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('payments/list.html', payments=payments)

@app.route('/payments/add/<int:member_id>', methods=['GET', 'POST'])
@login_required
def add_payment(member_id):
    """إضافة دفعة جديدة"""
    member = Member.query.get_or_404(member_id)
    form = PaymentForm()
    
    if form.validate_on_submit():
        # إنشاء رقم إيصال فريد
        receipt_number = f"REC{datetime.now().strftime('%Y%m%d')}{Payment.query.count() + 1:04d}"
        
        payment = Payment(
            member_id=member_id,
            amount=form.amount.data,
            payment_method=form.payment_method.data,
            description=form.description.data,
            receipt_number=receipt_number,
            created_by=current_user.id
        )
        
        db.session.add(payment)
        db.session.commit()
        
        flash(f'تم تسجيل الدفعة بنجاح برقم الإيصال {receipt_number}', 'success')
        return redirect(url_for('member_detail', id=member_id))
    
    return render_template('payments/add.html', form=form, member=member)

# مسارات API للبيانات الديناميكية
@app.route('/api/members/search')
@login_required
def api_members_search():
    """البحث في الأعضاء عبر API"""
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])
    
    members = Member.query.filter(
        db.or_(
            Member.full_name.contains(query),
            Member.member_id.contains(query),
            Member.phone.contains(query)
        )
    ).limit(10).all()
    
    return jsonify([{
        'id': member.id,
        'member_id': member.member_id,
        'full_name': member.full_name,
        'phone': member.phone,
        'status': member.membership_status
    } for member in members])

@app.errorhandler(404)
def not_found_error(error):
    """صفحة الخطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    db.session.rollback()
    return render_template('errors/500.html'), 500
