/* نمط CSS مخصص لنظام إدارة جيم الأبطال */

/* الخطوط العربية */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* الألوان الأساسية */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --gym-gold: #f1c40f;
    --gym-silver: #95a5a6;
}

/* تخصيص Bootstrap للعربية */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* البطاقات */
.card {
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
    border: none;
}

/* النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.stats-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* شارات الحالة */
.badge {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.status-active {
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
}

.status-expired {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
}

.status-expiring {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
}

/* الصور الشخصية */
.profile-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--light-color);
}

.profile-img-large {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid var(--light-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* التنقل بين الصفحات */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

/* الرسائل التنبيهية */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
    color: var(--warning-color);
    border-right: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
    color: var(--secondary-color);
    border-right: 4px solid var(--secondary-color);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.shadow-custom {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) 1;
}
