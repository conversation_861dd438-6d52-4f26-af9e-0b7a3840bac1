<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عضو جديد - جيم الأبطال</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-header h3 {
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .sidebar-menu .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .sidebar-menu .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            padding-right: 2rem;
        }
        
        .sidebar-menu .menu-item.active {
            background: rgba(255,255,255,0.2);
            border-right: 4px solid white;
        }
        
        .sidebar-menu .menu-item i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            border-radius: 15px;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-dumbbell fa-2x mb-2"></i>
            <h3>جيم الأبطال</h3>
            <p class="mb-0">نظام الإدارة</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="{{ url_for('index') }}" class="menu-item">
                <i class="fas fa-home"></i>
                الرئيسية
            </a>
            <a href="{{ url_for('members') }}" class="menu-item active">
                <i class="fas fa-users"></i>
                إدارة الأعضاء
            </a>
            <a href="{{ url_for('memberships') }}" class="menu-item">
                <i class="fas fa-id-card"></i>
                باقات العضوية
            </a>
            <a href="{{ url_for('payments') }}" class="menu-item">
                <i class="fas fa-credit-card"></i>
                المدفوعات
            </a>
            <a href="{{ url_for('trainers') }}" class="menu-item">
                <i class="fas fa-user-tie"></i>
                المدربين
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-calendar-alt"></i>
                جدولة الحصص
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-dumbbell"></i>
                إدارة المعدات
            </a>
            <a href="{{ url_for('reports') }}" class="menu-item">
                <i class="fas fa-chart-bar"></i>
                التقارير
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
            <a href="{{ url_for('logout') }}" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <div class="top-navbar">
            <div>
                <h4 class="mb-0" style="color: #667eea; font-weight: 700;">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة عضو جديد
                </h4>
            </div>
            <div>
                <span class="text-muted">مرحباً، </span>
                <strong style="color: #667eea;">{{ current_user.full_name }}</strong>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Content -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0" style="color: #667eea; font-weight: 700;">
                    <i class="fas fa-user-plus me-2"></i>
                    بيانات العضو الجديد
                </h5>
                <a href="{{ url_for('members') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل *</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">رقم الهاتف *</label>
                        <input type="tel" class="form-control" id="phone" name="phone" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                        <input type="date" class="form-control" id="birth_date" name="birth_date">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="gender" class="form-label">الجنس</label>
                        <select class="form-select" id="gender" name="gender">
                            <option value="">اختر الجنس</option>
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="emergency_contact" class="form-label">جهة الاتصال في الطوارئ</label>
                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ العضو
                    </button>
                    <a href="{{ url_for('members') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
